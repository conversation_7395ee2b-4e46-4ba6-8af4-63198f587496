"""
TTS Interrupt Monitoring System

This module provides real-time TTS playback monitoring with interrupt detection
and handling capabilities for the voice agent platform.
"""

import asyncio
import threading
import time
from typing import Dict, Any, Optional, Callable
from datetime import datetime

try:
    import pygame
    PYGAME_AVAILABLE = True
except ImportError:
    PYGAME_AVAILABLE = False

from core.logging.logger_config import get_module_logger
from schemas.outputSchema import StateOutput, StatusType, StatusCode


class TTSInterruptMonitor:
    """
    Monitors TTS playback and handles real-time interrupt detection.
    Provides pause/resume functionality and interrupt event handling.
    """
    
    def __init__(self, session_id: str, memory_manager, interrupt_config=None):
        self.session_id = session_id
        self.memory_manager = memory_manager
        self.interrupt_config = interrupt_config
        self.logger = get_module_logger("TTSInterruptMonitor", session_id=session_id)
        
        # Playback state
        self.is_playing = False
        self.is_paused = False
        self.current_audio_path = None
        self.playback_start_time = None
        self.pause_time = None
        self.total_pause_duration = 0
        
        # Interrupt detection
        self.interrupt_detected = False
        self.interrupt_callback = None
        self.monitoring_active = False
        
        # Threading for background monitoring
        self.monitor_thread = None
        self.stop_monitoring_event = threading.Event()

        # Initialize pygame if available
        if PYGAME_AVAILABLE:
            try:
                pygame.mixer.init()
                self.logger.info("Pygame mixer initialized for TTS playback")
            except Exception as e:
                self.logger.warning(f"Failed to initialize pygame mixer: {e}")
    
    async def start_tts_with_interrupt_monitoring(self, audio_path: str, 
                                                interrupt_callback: Optional[Callable] = None) -> StateOutput:
        """
        Start TTS playback with real-time interrupt monitoring.
        
        Args:
            audio_path: Path to the audio file to play
            interrupt_callback: Optional callback function for interrupt events
            
        Returns:
            StateOutput with monitoring status
        """
        try:
            self.logger.info(
                "Starting TTS with interrupt monitoring",
                action="start_tts_with_interrupt_monitoring",
                input_data={"audio_path": audio_path},
                layer="tts_interrupt_monitor"
            )
            
            # Reset state
            self.current_audio_path = audio_path
            self.interrupt_callback = interrupt_callback
            self.interrupt_detected = False
            self.is_playing = True
            self.is_paused = False
            self.playback_start_time = time.time()
            self.total_pause_duration = 0
            self.stop_monitoring_event.clear()
            
            # Store TTS playback state in memory
            await self.memory_manager.set_tts_playback_state(
                audio_path=audio_path,
                status="playing",
                playback_position=0.0
            )
            
            # Start background monitoring
            self.monitoring_active = True
            self.monitor_thread = threading.Thread(
                target=self._background_monitor_worker,
                daemon=True
            )
            self.monitor_thread.start()
            
            # Start audio playback if pygame is available
            if PYGAME_AVAILABLE:
                try:
                    pygame.mixer.music.load(audio_path)
                    pygame.mixer.music.play()
                    self.logger.info("Started pygame audio playback")
                except Exception as e:
                    self.logger.warning(f"Failed to start pygame playback: {e}")
            
            return StateOutput(
                status=StatusType.SUCCESS,
                message="TTS monitoring started successfully",
                code=StatusCode.OK,
                outputs={
                    "monitoring_active": True,
                    "audio_path": audio_path,
                    "playback_started": True
                },
                meta={"tts_monitor": "active"}
            )
            
        except Exception as e:
            self.logger.error(
                "Error starting TTS with interrupt monitoring",
                action="start_tts_with_interrupt_monitoring",
                reason=str(e),
                layer="tts_interrupt_monitor"
            )
            
            return StateOutput(
                status=StatusType.ERROR,
                message=f"Failed to start TTS monitoring: {str(e)}",
                code=StatusCode.INTERNAL_ERROR,
                outputs={},
                meta={"error": str(e)}
            )
    
    async def pause_tts_playback(self) -> StateOutput:
        """
        Pause the current TTS playback.
        
        Returns:
            StateOutput with pause status
        """
        try:
            if not self.is_playing or self.is_paused:
                return StateOutput(
                    status=StatusType.ERROR,
                    message="No active playback to pause",
                    code=StatusCode.BAD_REQUEST,
                    outputs={},
                    meta={"error": "no_active_playback"}
                )
            
            self.is_paused = True
            self.pause_time = time.time()
            
            # Pause pygame playback if available
            if PYGAME_AVAILABLE:
                try:
                    pygame.mixer.music.pause()
                    self.logger.info("Paused pygame audio playback")
                except Exception as e:
                    self.logger.warning(f"Failed to pause pygame playback: {e}")
            
            # Update memory state
            current_position = self.get_current_playback_position()
            await self.memory_manager.set_tts_playback_state(
                audio_path=self.current_audio_path,
                status="paused",
                playback_position=current_position
            )
            
            self.logger.info(
                "TTS playback paused",
                action="pause_tts_playback",
                output_data={"playback_position": current_position},
                layer="tts_interrupt_monitor"
            )
            
            return StateOutput(
                status=StatusType.SUCCESS,
                message="TTS playback paused",
                code=StatusCode.OK,
                outputs={
                    "paused": True,
                    "playback_position": current_position
                },
                meta={"playback_state": "paused"}
            )
            
        except Exception as e:
            self.logger.error(
                "Error pausing TTS playback",
                action="pause_tts_playback",
                reason=str(e),
                layer="tts_interrupt_monitor"
            )
            
            return StateOutput(
                status=StatusType.ERROR,
                message=f"Failed to pause TTS playback: {str(e)}",
                code=StatusCode.INTERNAL_ERROR,
                outputs={},
                meta={"error": str(e)}
            )
    
    async def resume_tts_playback(self, resume_from_position: Optional[float] = None) -> StateOutput:
        """
        Resume TTS playback from current or specified position.
        
        Args:
            resume_from_position: Optional position to resume from (in seconds)
            
        Returns:
            StateOutput with resume status
        """
        try:
            if not self.is_paused and resume_from_position is None:
                return StateOutput(
                    status=StatusType.ERROR,
                    message="No paused playback to resume",
                    code=StatusCode.BAD_REQUEST,
                    outputs={},
                    meta={"error": "no_paused_playback"}
                )
            
            # Calculate resume position
            if resume_from_position is not None:
                # Resume from specific position (for irreversible actions)
                self.playback_start_time = time.time() - resume_from_position
                self.total_pause_duration = 0
                resume_pos = resume_from_position
            else:
                # Resume from where we paused
                if self.pause_time:
                    self.total_pause_duration += time.time() - self.pause_time
                    self.pause_time = None
                resume_pos = self.get_current_playback_position()
            
            self.is_paused = False
            
            # Resume pygame playback if available
            if PYGAME_AVAILABLE:
                try:
                    if resume_from_position is not None:
                        # For specific position, reload and play from start
                        # (pygame doesn't support seeking, so this is a limitation)
                        pygame.mixer.music.load(self.current_audio_path)
                        pygame.mixer.music.play()
                    else:
                        pygame.mixer.music.unpause()
                    self.logger.info(f"Resumed pygame audio playback from {resume_pos:.2f}s")
                except Exception as e:
                    self.logger.warning(f"Failed to resume pygame playback: {e}")
            
            # Update memory state
            await self.memory_manager.set_tts_playback_state(
                audio_path=self.current_audio_path,
                status="playing",
                playback_position=resume_pos
            )
            
            self.logger.info(
                "TTS playback resumed",
                action="resume_tts_playback",
                output_data={"resume_position": resume_pos},
                layer="tts_interrupt_monitor"
            )
            
            return StateOutput(
                status=StatusType.SUCCESS,
                message="TTS playback resumed",
                code=StatusCode.OK,
                outputs={
                    "resumed": True,
                    "playback_position": resume_pos
                },
                meta={"playback_state": "playing"}
            )
            
        except Exception as e:
            self.logger.error(
                "Error resuming TTS playback",
                action="resume_tts_playback",
                reason=str(e),
                layer="tts_interrupt_monitor"
            )
            
            return StateOutput(
                status=StatusType.ERROR,
                message=f"Failed to resume TTS playback: {str(e)}",
                code=StatusCode.INTERNAL_ERROR,
                outputs={},
                meta={"error": str(e)}
            )
    
    async def stop_monitoring(self) -> StateOutput:
        """
        Stop TTS monitoring and playback.
        
        Returns:
            StateOutput with stop status
        """
        try:
            self.monitoring_active = False
            self.stop_monitoring_event.set()
            self.is_playing = False
            self.is_paused = False
            
            # Stop pygame playback if available
            if PYGAME_AVAILABLE:
                try:
                    pygame.mixer.music.stop()
                    self.logger.info("Stopped pygame audio playback")
                except Exception as e:
                    self.logger.warning(f"Failed to stop pygame playback: {e}")
            
            # Wait for monitor thread to finish
            if self.monitor_thread and self.monitor_thread.is_alive():
                self.monitor_thread.join(timeout=2.0)
            
            # Update memory state
            await self.memory_manager.set_tts_playback_state(
                audio_path=self.current_audio_path,
                status="stopped",
                playback_position=self.get_current_playback_position()
            )
            
            self.logger.info(
                "TTS monitoring stopped",
                action="stop_monitoring",
                layer="tts_interrupt_monitor"
            )
            
            return StateOutput(
                status=StatusType.SUCCESS,
                message="TTS monitoring stopped",
                code=StatusCode.OK,
                outputs={"monitoring_stopped": True},
                meta={"tts_monitor": "stopped"}
            )
            
        except Exception as e:
            self.logger.error(
                "Error stopping TTS monitoring",
                action="stop_monitoring",
                reason=str(e),
                layer="tts_interrupt_monitor"
            )
            
            return StateOutput(
                status=StatusType.ERROR,
                message=f"Failed to stop TTS monitoring: {str(e)}",
                code=StatusCode.INTERNAL_ERROR,
                outputs={},
                meta={"error": str(e)}
            )
    
    def get_current_playback_position(self) -> float:
        """Get current playback position in seconds."""
        if not self.playback_start_time:
            return 0.0
        
        elapsed = time.time() - self.playback_start_time - self.total_pause_duration
        if self.is_paused and self.pause_time:
            elapsed -= (time.time() - self.pause_time)
        
        return max(0.0, elapsed)
    
    def _background_monitor_worker(self):
        """Background worker thread for interrupt monitoring."""
        try:
            while self.monitoring_active and not self.stop_monitoring_event.is_set():
                # Check for interrupt conditions
                # This is where you would integrate real VAD or other interrupt detection
                
                # For now, simulate checking every 100ms
                time.sleep(0.1)
                
                # Check if playback is complete
                if PYGAME_AVAILABLE and not pygame.mixer.music.get_busy() and self.is_playing:
                    self.is_playing = False
                    self.monitoring_active = False
                    break
                    
        except Exception as e:
            self.logger.error(
                "Error in background monitor worker",
                action="_background_monitor_worker",
                reason=str(e),
                layer="tts_interrupt_monitor"
            )
