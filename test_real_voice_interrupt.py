#!/usr/bin/env python3
"""
Test script for real-time voice-based interrupt system.
This test demonstrates the complete voice interrupt flow with real microphone input.
"""

import asyncio
import sys
import os

# Add the project root to the path
sys.path.append(os.path.abspath('.'))

async def test_real_voice_interrupt():
    """Test real-time voice interrupt system with microphone input."""
    
    print("🎤 Testing Real-Time Voice Interrupt System")
    print("=" * 60)
    print()
    print("This test will:")
    print("1. Start TTS playback")
    print("2. Monitor your microphone for voice activity")
    print("3. When you speak, it will:")
    print("   - Pause the TTS")
    print("   - Record your voice")
    print("   - Transcribe what you said")
    print("   - Process it as a new workflow")
    print()
    print("🔊 Make sure your microphone is working!")
    print("🎧 You may want to use headphones to avoid feedback")
    print()
    
    try:
        # Import required modules
        from core.state_manager.state_manager import StateManager
        
        # Create state manager
        workflow_name = "banking_workflow.json"
        session_id = "real_voice_interrupt_test"
        user_id = "voice_test_user"
        
        print(f"📋 Creating StateManager with workflow: {workflow_name}")
        state_manager = await StateManager.create(workflow_name, session_id, user_id)
        
        print("\n🎵 Starting TTS playback...")
        print("💬 The system will say a greeting message")
        print("🗣️  INTERRUPT BY SPEAKING when you hear the TTS!")
        print("   Try saying something like:")
        print("   - 'What's my account balance?'")
        print("   - 'Transfer money'")
        print("   - 'Apply for a loan'")
        print()
        
        # Start the pipeline with TTS that can be interrupted
        print("🚀 Starting pipeline...")
        
        # Simulate initial audio input (you could replace this with real audio)
        # For this test, we'll use a simple text input to start the greeting
        initial_input = "Hello, I need help with banking"
        
        # Step 1: STT (simulate with text)
        print("[PIPELINE] Step 1: STT (simulated)")
        await state_manager.transitionPipeline("stt")
        # We'll skip actual STT and use the text directly
        
        # Step 2: Preprocessing
        print("[PIPELINE] Step 2: Preprocessing")
        await state_manager.transitionPipeline("preprocessing")
        preprocessing_result = await state_manager.executePipelineState({"transcript": initial_input})
        
        # Step 3: Processing
        print("[PIPELINE] Step 3: Processing")
        await state_manager.transitionPipeline("processing")
        if preprocessing_result.status.value == "success":
            clean_text = preprocessing_result.outputs.get("clean_text", initial_input)
            intent = preprocessing_result.outputs.get("intent", "greeting")
            processing_result = await state_manager.executePipelineState({
                "clean_text": clean_text, 
                "intent": intent
            })
        
        # Step 4: TTS with interrupt monitoring
        print("[PIPELINE] Step 4: TTS with Real-Time Interrupt Monitoring")
        print("🎤 SPEAK NOW TO INTERRUPT THE TTS!")
        await state_manager.transitionPipeline("tts")
        
        if processing_result.status.value == "success":
            ai_response = processing_result.outputs.get("llm_answer", "Hello! Welcome to our banking system. How can I help you today?")
            print(f"[TTS] Response: {ai_response}")
            
            # This will start TTS with real-time microphone monitoring
            tts_result = await state_manager.executePipelineState({"text": ai_response})
            
            print("\n✅ Pipeline completed!")
            print("📊 Results:")
            print(f"   - TTS Status: {tts_result.status}")
            print(f"   - Audio Generated: {tts_result.outputs.get('audio_path', 'N/A')}")
            
        print("\n🏁 Test completed!")
        print("If you interrupted the TTS by speaking, you should have seen:")
        print("   - '[INTERRUPT] Voice detected! Triggering interrupt...'")
        print("   - '[MIC] Recording X seconds of interrupt audio...'")
        print("   - '[MIC] Transcription successful: [your words]'")
        print("   - '[QUEUE] Processing queued user input: [your words]'")
        print("   - A new workflow starting with your spoken input")
        
    except ImportError as e:
        print(f"❌ Missing dependency: {e}")
        print("💡 Install required packages:")
        print("   pip install sounddevice")
        print("   pip install numpy")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("🎤 Real-Time Voice Interrupt Test")
    print("Press Ctrl+C to stop at any time")
    print()
    
    try:
        asyncio.run(test_real_voice_interrupt())
    except KeyboardInterrupt:
        print("\n⏹️  Test stopped by user")
    except Exception as e:
        print(f"\n❌ Test error: {e}")
