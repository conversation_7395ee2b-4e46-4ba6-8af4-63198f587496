#!/usr/bin/env python3
"""
Test script to verify improved intent classification for banking interrupts.
"""

import asyncio
import sys
import os

# Add the project root to the path
sys.path.append(os.path.abspath('.'))

async def test_intent_classification():
    """Test the improved intent classification with banking context."""
    
    print("🧠 Testing Improved Intent Classification")
    print("=" * 50)
    
    try:
        from agents.processing.preprocessing_agent import PreprocessingAgent
        
        # Create preprocessing agent
        agent = PreprocessingAgent(
            session_id="intent_test",
            user_id="test_user",
            memory_manager=None  # We'll test without memory first
        )
        
        # Test cases that should now work better
        test_cases = [
            {
                "input": "dollar please in dollar",
                "expected": "account_balance",
                "context": [
                    {"role": "user", "text": "What's in my bank account?"},
                    {"role": "ai", "text": "Let me check your account balance..."}
                ]
            },
            {
                "input": "in USD please",
                "expected": "account_balance", 
                "context": [
                    {"role": "user", "text": "Check my balance"},
                    {"role": "ai", "text": "Your balance is..."}
                ]
            },
            {
                "input": "What's my account balance?",
                "expected": "account_balance",
                "context": []
            },
            {
                "input": "Transfer money",
                "expected": "fund_transfer",
                "context": []
            },
            {
                "input": "Apply for a loan",
                "expected": "loan_application",
                "context": []
            }
        ]
        
        print("🧪 Running Intent Classification Tests...")
        print()
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"Test {i}: '{test_case['input']}'")
            print(f"Expected: {test_case['expected']}")
            
            try:
                # Test the improved classification
                result = await agent.classify_intent(
                    test_case["input"], 
                    test_case["context"]
                )
                
                print(f"Got: {result}")
                
                if result == test_case["expected"]:
                    print("✅ PASS")
                else:
                    print("❌ FAIL")
                    
            except Exception as e:
                print(f"❌ ERROR: {e}")
                
            print("-" * 30)
            print()
        
        print("🎯 Key Improvements:")
        print("1. Banking-specific intent examples")
        print("2. USD/dollar keywords → account_balance")
        print("3. Conversation context awareness")
        print("4. Exact intent matching for workflow")
        
    except ImportError as e:
        print(f"❌ Missing dependency: {e}")
        print("💡 Make sure OpenAI API key is set")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("🧠 Intent Classification Test")
    print("Testing improved banking intent recognition...")
    print()
    
    try:
        asyncio.run(test_intent_classification())
    except KeyboardInterrupt:
        print("\n⏹️  Test stopped by user")
    except Exception as e:
        print(f"\n❌ Test error: {e}")
