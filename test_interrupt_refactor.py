#!/usr/bin/env python3
"""
Test script to verify the interrupt system refactoring works correctly.
This test validates the new InterruptManager and TTSInterruptMonitor classes.
"""

import asyncio
import sys
import os
from dotenv import load_dotenv
load_dotenv()

# Add the project root to the path
sys.path.append(os.path.abspath('.'))

async def test_interrupt_refactor():
    """Test the refactored interrupt system components."""
    
    print("🧪 Testing Interrupt System Refactoring")
    print("=" * 50)
    
    try:
        # Test 1: Import the new classes
        print("📦 Testing imports...")
        from core.interruption.interrupt_manager import InterruptManager
        from core.interruption.tts_interrupt_monitor import TTSInterruptMonitor
        from core.memory.memory_manager import MemoryManager
        from core.config.interrupt_config import get_interrupt_config
        print("✅ All imports successful")
        
        # Test 2: Initialize components
        print("\n🔧 Testing component initialization...")
        session_id = "test_refactor_session"
        memory_manager = MemoryManager(session_id, "test_user")
        interrupt_config = get_interrupt_config()
        
        interrupt_manager = InterruptManager(session_id, memory_manager, interrupt_config)
        tts_monitor = TTSInterruptMonitor(session_id, memory_manager, interrupt_config)
        print("✅ Components initialized successfully")
        
        # Test 3: Test InterruptManager functionality
        print("\n🎯 Testing InterruptManager...")
        
        # Test interrupt event handling
        test_interrupt_data = {
            "session_id": session_id,
            "user_input": "What's my balance?",
            "playback_position": 2.5,
            "audio_path": "test_audio.wav",
            "action_reversible": True,
            "confirmed": True,
            "timestamp": "2025-01-19T10:30:00Z"
        }
        
        result = await interrupt_manager.handle_interrupt_event(test_interrupt_data)
        print(f"   Interrupt handling result: {result.status}")
        print(f"   Message: {result.message}")
        
        # Test interrupt status
        status = interrupt_manager.get_interrupt_status()
        print(f"   Interrupt status: {status}")
        print("✅ InterruptManager tests passed")
        
        # Test 4: Test TTSInterruptMonitor functionality
        print("\n🎵 Testing TTSInterruptMonitor...")
        
        # Test TTS monitoring start
        monitor_result = await tts_monitor.start_tts_with_interrupt_monitoring(
            "test_audio.wav",
            interrupt_callback=None
        )
        print(f"   TTS monitoring result: {monitor_result.status}")
        print(f"   Message: {monitor_result.message}")
        
        # Test pause functionality
        pause_result = await tts_monitor.pause_tts_playback()
        print(f"   Pause result: {pause_result.status}")
        
        # Test resume functionality
        resume_result = await tts_monitor.resume_tts_playback()
        print(f"   Resume result: {resume_result.status}")
        
        # Test stop functionality
        stop_result = await tts_monitor.stop_monitoring()
        print(f"   Stop result: {stop_result.status}")
        print("✅ TTSInterruptMonitor tests passed")
        
        # Test 5: Test memory retrieval fix
        print("\n🧠 Testing memory retrieval fix...")
        
        # Store test data in contextual memory
        await memory_manager.set("contextual", "last_user_message", "Check my account balance")
        await memory_manager.set("contextual", "last_user_intent", "check_balance")
        
        # Test direct contextual memory access (the fix)
        last_message = await memory_manager.contextual.get("last_user_message")
        last_intent = await memory_manager.contextual.get("last_user_intent")
        
        print(f"   Retrieved last_user_message: '{last_message}'")
        print(f"   Retrieved last_user_intent: '{last_intent}'")
        
        if last_message and last_intent:
            print("✅ Memory retrieval fix working correctly")
        else:
            print("❌ Memory retrieval fix not working")
        
        # Test 6: Test VAD configuration
        print("\n🎤 Testing VAD configuration...")
        vad_threshold = interrupt_config.global_settings.vad_threshold
        confirmation_window = interrupt_config.global_settings.confirmation_window_seconds
        enabled = interrupt_config.global_settings.enabled
        
        print(f"   VAD threshold: {vad_threshold}")
        print(f"   Confirmation window: {confirmation_window}s")
        print(f"   Interrupt detection enabled: {enabled}")
        print("✅ VAD configuration accessible")
        
        print("\n🎉 All refactoring tests passed!")
        print("\n📋 Test Summary:")
        print("   ✅ New InterruptManager class working")
        print("   ✅ New TTSInterruptMonitor class working")
        print("   ✅ Memory retrieval fix implemented")
        print("   ✅ VAD configuration accessible")
        print("   ✅ Component integration successful")
        
        return True
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_state_manager_integration():
    """Test that StateManager can use the new interrupt components."""
    
    print("\n🔗 Testing StateManager Integration")
    print("=" * 40)
    
    try:
        from core.state_manager.state_manager import StateManager
        
        # Create a test state manager
        print("📋 Creating StateManager...")
        state_manager = await StateManager.create(
            workflow_name="banking_workflow.json",
            session_id="test_integration",
            user_id="test_user"
        )
        
        # Check if new components are initialized
        if hasattr(state_manager, 'interrupt_manager'):
            print("✅ InterruptManager integrated")
        else:
            print("❌ InterruptManager not found")
            
        if hasattr(state_manager, 'tts_monitor'):
            print("✅ TTSInterruptMonitor integrated")
        else:
            print("❌ TTSInterruptMonitor not found")
        
        # Test interrupt status
        if hasattr(state_manager, 'interrupt_manager'):
            status = state_manager.interrupt_manager.get_interrupt_status()
            print(f"   Interrupt system status: {status['interrupt_config_enabled']}")
        
        print("✅ StateManager integration successful")
        return True
        
    except Exception as e:
        print(f"❌ StateManager integration failed: {e}")
        return False

async def main():
    """Run all tests."""
    print("🚀 Starting Interrupt System Refactoring Tests")
    
    # Run component tests
    component_test_passed = await test_interrupt_refactor()
    
    # Run integration tests
    integration_test_passed = await test_state_manager_integration()
    
    if component_test_passed and integration_test_passed:
        print("\n🎉 ALL TESTS PASSED!")
        print("The interrupt system refactoring is working correctly.")
    else:
        print("\n❌ SOME TESTS FAILED!")
        print("Please check the error messages above.")

if __name__ == "__main__":
    asyncio.run(main())
